<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOMACA • Générateur de Codes-Barres</title>
    <link rel="icon" type="image/png" href="exel.png">
    <link rel="shortcut icon" type="image/png" href="exel.png">
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- En-tête SOMACA -->
        <header class="header">
            <div class="header-content">
                <div class="logo-container">
                    <div class="logo">
                        <i class="fas fa-industry"></i>
                        <span>SOMACA</span>
                    </div>
                </div>
                <div class="header-title">
                    <h1><i class="fas fa-qrcode"></i> Générateur Excel de Codes-Barres</h1>
                    <p><i class="fas fa-building"></i> Société Marocaine de Construction Automobile • Groupe Renault</p>
                </div>
            </div>
        </header>

        <!-- Contenu principal -->
        <main class="main-content">
            <div class="content-grid">
                <!-- Section Fichiers -->
                <div class="card files-card">
                    <div class="card-header">
                        <h2><i class="fas fa-folder-open"></i> Fichiers</h2>
                    </div>
                    <div class="card-body">
                        <!-- Fichier source -->
                        <div class="input-group">
                            <label for="source-file">
                                <i class="fas fa-file-excel"></i> Fichier Excel source
                            </label>
                            <div class="file-input-container">
                                <input type="text" id="source-file" placeholder="Sélectionnez votre fichier Excel..." readonly>
                                <input type="file" id="file-input" accept=".xlsx,.xls" style="display: none;" onchange="handleFileSelect(this)">
                                <button class="btn btn-secondary" onclick="selectFileMethod()">
                                    <i class="fas fa-folder-open"></i> Parcourir
                                </button>
                            </div>
                        </div>

                        <!-- Dossier destination -->
                        <div class="input-group">
                            <label for="dest-folder">
                                <i class="fas fa-folder"></i> Dossier de destination
                            </label>
                            <div class="file-input-container">
                                <input type="text" id="dest-folder" placeholder="Choisissez le dossier de destination..." readonly>
                                <button class="btn btn-secondary" onclick="selectFolder()">
                                    <i class="fas fa-folder"></i> Choisir
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Options -->
                <div class="card options-card">
                    <div class="card-header">
                        <h2><i class="fas fa-cogs"></i> Options</h2>
                    </div>
                    <div class="card-body">
                        <!-- Checkboxes -->
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="generate-barcodes" checked>
                                <label for="generate-barcodes">
                                    <i class="fas fa-barcode"></i> Ajouter colonne codes-barres (Code128)
                                </label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="generate-qr" checked>
                                <label for="generate-qr">
                                    <i class="fas fa-qrcode"></i> Ajouter colonne QR codes
                                </label>
                            </div>
                        </div>



                        <!-- Boutons d'action -->
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-generate" onclick="startGeneration()">
                                <span>🚀</span> Générer les Codes
                            </button>

                            <button class="btn btn-success btn-renault" onclick="generateRenaultEtiquettes()" disabled>
                                <span>🏷️</span> Étiquettes Renault
                            </button>

                            <div class="print-options">
                                <button class="btn btn-secondary btn-print" onclick="showPrintOptions()" disabled id="btn-print">
                                    <i class="fas fa-print"></i> Options d'Impression
                                </button>
                            </div>

                            <button class="btn btn-danger btn-cancel" onclick="cancelOperation()" disabled id="btn-cancel" style="display: none;">
                                <i class="fas fa-stop"></i> Annuler l'opération
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Progression -->
            <div class="card progress-card">
                <div class="card-header">
                    <h2><i class="fas fa-chart-line"></i> Progression</h2>
                </div>
                <div class="card-body">
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                            <span class="progress-text" id="progress-text">0%</span>
                        </div>
                    </div>
                    <div class="status-message" id="status-message">
                        <span>🎯</span> Prêt à générer les codes
                    </div>
                </div>
            </div>
        </main>

        <!-- Pied de page -->
        <footer class="footer">
            <p>SOMACA • Société Marocaine de Construction Automobile • Groupe Renault</p>
            <div class="developer-signature">
                <p>Développé par <strong>IMAD ELberrouagui</strong></p>
                <p class="signature">© 2024 - Application SOMACA</p>
            </div>
        </footer>
    </div>

    <!-- Messages de notification -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <span class="notification-message" id="notification-message"></span>
            <button class="notification-close" onclick="closeNotification()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Dialogue de fichier existant amélioré -->
    <div class="modal-overlay" id="file-exists-modal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>Fichier Existant</h3>
                <p class="modal-subtitle">Conflit de nom détecté</p>
            </div>
            <div class="modal-body">
                <div class="file-info">
                    <i class="fas fa-file-excel"></i>
                    <div class="file-details">
                        <p class="file-question">Le fichier suivant existe déjà :</p>
                        <p class="filename" id="existing-filename"></p>
                        <p class="location">dans le dossier de destination</p>
                    </div>
                </div>
                <div class="choice-question">
                    <h4>Comment souhaitez-vous procéder ?</h4>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-replace" onclick="handleFileChoice('overwrite')">
                    <div class="btn-content">
                        <i class="fas fa-sync-alt"></i>
                        <div class="btn-text">
                            <span class="btn-title">Remplacer</span>
                            <span class="btn-desc">Écraser l'ancien fichier</span>
                        </div>
                    </div>
                </button>
                <button class="btn btn-rename" onclick="handleFileChoice('rename')">
                    <div class="btn-content">
                        <i class="fas fa-plus-circle"></i>
                        <div class="btn-text">
                            <span class="btn-title">Nouveau nom</span>
                            <span class="btn-desc">Créer avec timestamp</span>
                        </div>
                    </div>
                </button>
                <button class="btn btn-cancel" onclick="handleFileChoice('cancel')">
                    <div class="btn-content">
                        <i class="fas fa-times-circle"></i>
                        <div class="btn-text">
                            <span class="btn-title">Annuler</span>
                            <span class="btn-desc">Arrêter la génération</span>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Options d'Impression Élégante -->
    <div class="modal-overlay" id="print-options-modal" style="display: none;">
        <div class="modal-dialog modal-elegant">
            <div class="modal-header-elegant">
                <div class="modal-icon-elegant">
                    <i class="fas fa-print"></i>
                </div>
                <h3>Options d'Impression</h3>
                <p class="modal-subtitle-elegant">Choisissez votre méthode</p>
            </div>
            <div class="modal-buttons-elegant">
                <button class="btn-elegant btn-export-elegant" onclick="handlePrintChoice('export')">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Exporter</span>
                        <span class="btn-desc-elegant">Sauvegarder images</span>
                    </div>
                </button>
                <button class="btn-elegant btn-print-elegant" onclick="handlePrintChoice('print')">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-print"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Imprimer</span>
                        <span class="btn-desc-elegant">Direct sur A4</span>
                    </div>
                </button>

                <button class="btn-elegant btn-cancel-elegant" onclick="handlePrintChoice('cancel')">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Annuler</span>
                        <span class="btn-desc-elegant">Fermer</span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Options d'Export -->
    <div class="modal-overlay" id="export-options-modal" style="display: none;">
        <div class="modal-dialog modal-elegant">
            <div class="modal-header-elegant">
                <div class="modal-icon-elegant">
                    <i class="fas fa-download"></i>
                </div>
                <h3>Options d'Export</h3>
                <p class="modal-subtitle-elegant">Choisissez les types à exporter</p>
            </div>
            <div class="modal-content-elegant">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="generate-barcodes" checked>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">Codes-barres</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="generate-qr" checked>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">QR Codes</span>
                    </label>
                </div>
            </div>
            <div class="modal-buttons-elegant">
                <button class="btn-elegant btn-export-elegant" onclick="exportImages()">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Exporter</span>
                        <span class="btn-desc-elegant">Sauvegarder</span>
                    </div>
                </button>
                <button class="btn-elegant btn-cancel-elegant" onclick="closeExportOptions()">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Annuler</span>
                        <span class="btn-desc-elegant">Fermer</span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Impression Directe -->
    <div class="modal-overlay" id="direct-print-modal" style="display: none;">
        <div class="modal-dialog modal-elegant">
            <div class="modal-header-elegant">
                <div class="modal-icon-elegant">
                    <i class="fas fa-print"></i>
                </div>
                <h3>Impression Directe</h3>
                <p class="modal-subtitle-elegant">Imprimer directement sur A4</p>
            </div>
            <div class="modal-content-elegant">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="print-barcodes" checked>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">Codes-barres</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="print-qr" checked>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">QR Codes</span>
                    </label>
                </div>
            </div>
            <div class="modal-buttons-elegant">
                <button class="btn-elegant btn-print-elegant" onclick="printDirectly()">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-print"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Imprimer</span>
                        <span class="btn-desc-elegant">Direct A4</span>
                    </div>
                </button>
                <button class="btn-elegant btn-cancel-elegant" onclick="closeDirectPrint()">
                    <div class="btn-icon-elegant">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="btn-text-elegant">
                        <span class="btn-title-elegant">Annuler</span>
                        <span class="btn-desc-elegant">Fermer</span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <script src="script_native.js"></script>
</body>
</html>
