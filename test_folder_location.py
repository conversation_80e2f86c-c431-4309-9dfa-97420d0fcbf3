#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'emplacement des dossiers Tickets Renault
Développé par: <PERSON><PERSON>
"""

import os
import sys

def test_folder_location():
    """Test de la création du dossier au bon emplacement"""
    try:
        from app_native import SomacaGenerator
        
        print("=== Test Emplacement Dossier Tickets Renault ===")
        
        # Créer une instance du générateur
        generator = SomacaGenerator()
        
        # Simuler un fichier Excel existant dans le répertoire courant
        test_excel_path = "test_somaca_simple.xlsx"
        
        if os.path.exists(test_excel_path):
            print(f"✅ Fichier Excel test trouvé: {test_excel_path}")
            
            # Simuler que ce fichier a été généré
            generator.last_generated_file = test_excel_path
            
            # Obtenir les informations sur l'emplacement attendu
            excel_dir = os.path.dirname(test_excel_path)
            excel_base_name = os.path.splitext(os.path.basename(test_excel_path))[0]
            expected_folder = os.path.join(excel_dir, f"{excel_base_name}_Tickets_Renault")
            
            print(f"📁 Répertoire Excel: {excel_dir}")
            print(f"📄 Nom de base Excel: {excel_base_name}")
            print(f"📂 Dossier attendu: {expected_folder}")
            
            # Test de génération avec couleur noire
            print("\n🧪 Test génération tickets...")
            result = generator.generate_renault_tickets_with_colors(['black'])
            
            if result.get('success'):
                print("✅ Génération réussie!")
                print(f"📂 Dossier créé: {result.get('folder', 'Non spécifié')}")
                
                # Vérifier que le dossier existe
                if 'folder' in result and os.path.exists(result['folder']):
                    print("✅ Dossier existe bien sur le disque")
                    
                    # Vérifier l'emplacement
                    actual_folder = result['folder']
                    if actual_folder == expected_folder:
                        print("✅ Dossier créé au bon emplacement!")
                    else:
                        print(f"❌ Emplacement incorrect:")
                        print(f"   Attendu: {expected_folder}")
                        print(f"   Actuel:  {actual_folder}")
                        
                    # Lister le contenu
                    try:
                        content = os.listdir(actual_folder)
                        print(f"📋 Contenu du dossier: {content}")
                    except Exception as e:
                        print(f"❌ Erreur lecture contenu: {e}")
                        
                else:
                    print("❌ Dossier n'existe pas sur le disque")
            else:
                print(f"❌ Échec génération: {result.get('message', 'Erreur inconnue')}")
                
        else:
            print(f"❌ Fichier Excel test non trouvé: {test_excel_path}")
            print("📝 Créons un fichier test...")
            
            # Créer un fichier Excel simple pour le test
            try:
                import pandas as pd
                
                # Données de test
                test_data = {
                    'Colonne1': ['Test1', 'Test2', 'Test3'],
                    'Colonne2': ['Data1', 'Data2', 'Data3'],
                    'Colonne3': ['Info1', 'Info2', 'Info3']
                }
                
                df = pd.DataFrame(test_data)
                df.to_excel(test_excel_path, index=False)
                
                print(f"✅ Fichier test créé: {test_excel_path}")
                
                # Relancer le test
                generator.last_generated_file = test_excel_path
                result = generator.generate_renault_tickets_with_colors(['black'])
                
                if result.get('success'):
                    print("✅ Test avec fichier créé réussi!")
                    print(f"📂 Dossier: {result.get('folder')}")
                else:
                    print(f"❌ Test échoué: {result.get('message')}")
                    
            except Exception as e:
                print(f"❌ Erreur création fichier test: {e}")
        
        print("\n=== Test terminé ===")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_folder_location()
