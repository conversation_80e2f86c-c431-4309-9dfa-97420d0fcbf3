#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide pour vérifier le nouveau titre SOMACA dans les tickets
"""

import pandas as pd
import os

def create_test_file():
    """Créer un fichier Excel de test pour les tickets SOMACA"""
    
    # Données de test
    data = {
        'Nom': ['Pièce A', 'Pièce B', 'Pièce C'],
        'Référence': ['REF001', 'REF002', 'REF003'],
        'Description': ['Description A', 'Description B', 'Description C'],
        'Quantité': [10, 20, 15]
    }
    
    # Créer DataFrame
    df = pd.DataFrame(data)
    
    # Sauvegarder
    filename = 'test_titre_somaca.xlsx'
    df.to_excel(filename, index=False)
    
    print(f"✅ Fichier de test créé: {filename}")
    print(f"📊 Contenu:")
    print(df)
    
    return filename

if __name__ == '__main__':
    create_test_file()
