<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Renault</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .log {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🧪 Test Modal Renault - Fermeture</h1>
    
    <button class="test-button" onclick="testShowModal()">
        <i class="fas fa-eye"></i> Afficher Modal
    </button>
    
    <button class="test-button" onclick="testCloseModal()">
        <i class="fas fa-times"></i> Fermer Modal
    </button>
    
    <button class="test-button" onclick="clearLog()">
        <i class="fas fa-trash"></i> Vider Log
    </button>
    
    <div class="log" id="log">
        <div class="log-entry log-info">📋 Log des tests - Prêt</div>
    </div>

    <script>
        // Fonction de log
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="log-entry log-info">📋 Log vidé</div>';
        }

        // Copier les fonctions de la modal depuis script_native.js
        function showRenaultChoiceModal() {
            addLog('🚀 Début showRenaultChoiceModal()', 'info');
            
            const modalHtml = `
                <div class="modal-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); display: flex; justify-content: center; align-items: center; z-index: 1000; backdrop-filter: blur(3px);">
                    <div class="modal-content" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); padding: 40px; border-radius: 15px; max-width: 550px; width: 90%; box-shadow: 0 20px 40px rgba(0,0,0,0.15); border: 1px solid #e9ecef; animation: modalSlideIn 0.3s ease-out;">

                        <!-- En-tête avec icône -->
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="background: linear-gradient(135deg, #007bff, #0056b3); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(0,123,255,0.3);">
                                <i class="fas fa-palette" style="font-size: 35px; color: white;"></i>
                            </div>
                            <h3 style="margin: 0; color: #2c3e50; font-size: 24px; font-weight: 600;">Tickets Renault</h3>
                            <p style="color: #6c757d; margin: 8px 0 0 0; font-size: 16px;">Choisissez la couleur de fond pour vos tickets</p>
                        </div>

                        <!-- Options de couleur -->
                        <div style="margin: 30px 0;">
                            <label class="color-option" style="display: flex; align-items: center; margin-bottom: 20px; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 12px; transition: all 0.3s ease; background: white;">
                                <input type="checkbox" id="background-black" checked style="margin-right: 15px; transform: scale(1.3);">
                                <div style="display: flex; align-items: center; flex: 1;">
                                    <div style="background: #000; color: white; padding: 12px 20px; border-radius: 8px; font-size: 16px; font-weight: 600; margin-right: 15px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                                        ⚫ Fond Noir
                                    </div>
                                </div>
                            </label>

                            <label class="color-option" style="display: flex; align-items: center; margin-bottom: 20px; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 12px; transition: all 0.3s ease; background: white;">
                                <input type="checkbox" id="background-white" style="margin-right: 15px; transform: scale(1.3);">
                                <div style="display: flex; align-items: center; flex: 1;">
                                    <div style="background: #fff; color: #333; padding: 12px 20px; border-radius: 8px; font-size: 16px; font-weight: 600; margin-right: 15px; border: 2px solid #dee2e6; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                        ⚪ Fond Blanc
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Boutons -->
                        <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                            <button onclick="testCloseModal()" style="padding: 12px 25px; border: 2px solid #6c757d; background: transparent; color: #6c757d; border-radius: 8px; cursor: pointer; font-weight: 500;">
                                <i class="fas fa-times" style="margin-right: 8px;"></i>Annuler
                            </button>
                            <button onclick="testGenerateRenault()" style="padding: 12px 25px; border: none; background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 8px; cursor: pointer; font-weight: 600;">
                                <i class="fas fa-rocket" style="margin-right: 8px;"></i>Générer les Tickets
                            </button>
                        </div>
                    </div>
                </div>

                <style>
                    @keyframes modalSlideIn {
                        from {
                            opacity: 0;
                            transform: translateY(-50px) scale(0.9);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0) scale(1);
                        }
                    }
                </style>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            addLog('✅ Modal ajoutée au DOM', 'success');
        }

        function closeRenaultChoiceModal() {
            addLog('🔄 Début closeRenaultChoiceModal()', 'info');
            
            // Supprimer toutes les modals immédiatement
            const modals = document.querySelectorAll('.modal-overlay');
            addLog(`🔍 Trouvé ${modals.length} modal(s)`, 'info');
            
            modals.forEach((modal, index) => {
                addLog(`🗑️ Suppression modal ${index + 1}`, 'info');
                modal.remove();
            });
            
            // Double vérification
            const modalsByClass = document.getElementsByClassName('modal-overlay');
            addLog(`🔍 Vérification: ${modalsByClass.length} modal(s) restante(s)`, 'info');
            
            while (modalsByClass.length > 0) {
                addLog('🗑️ Suppression modal restante', 'info');
                modalsByClass[0].remove();
            }
            
            addLog('✅ Modal fermée', 'success');
        }

        function testGenerateRenault() {
            addLog('🎯 Test génération Renault', 'info');
            
            const blackCheckbox = document.getElementById('background-black');
            const whiteCheckbox = document.getElementById('background-white');
            
            addLog(`📋 Checkbox noir: ${blackCheckbox ? 'trouvé' : 'non trouvé'}`, blackCheckbox ? 'success' : 'error');
            addLog(`📋 Checkbox blanc: ${whiteCheckbox ? 'trouvé' : 'non trouvé'}`, whiteCheckbox ? 'success' : 'error');
            
            if (blackCheckbox) addLog(`✓ Noir sélectionné: ${blackCheckbox.checked}`, 'info');
            if (whiteCheckbox) addLog(`✓ Blanc sélectionné: ${whiteCheckbox.checked}`, 'info');
            
            // Fermer la modal
            addLog('🚪 Fermeture de la modal...', 'info');
            closeRenaultChoiceModal();
            
            addLog('🎉 Test terminé - Modal devrait être fermée', 'success');
        }

        // Fonctions de test
        function testShowModal() {
            addLog('🧪 Test: Affichage modal', 'info');
            showRenaultChoiceModal();
        }

        function testCloseModal() {
            addLog('🧪 Test: Fermeture modal', 'info');
            closeRenaultChoiceModal();
        }
    </script>
</body>
</html>
