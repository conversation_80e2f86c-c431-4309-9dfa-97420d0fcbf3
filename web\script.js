// Variables globales
let isGenerating = false;

// Fonctions utilitaires
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const messageElement = document.getElementById('notification-message');
    
    messageElement.textContent = message;
    notification.className = `notification show ${type}`;
    
    // Auto-fermeture après 5 secondes
    setTimeout(() => {
        closeNotification();
    }, 5000);
}

function closeNotification() {
    const notification = document.getElementById('notification');
    notification.classList.remove('show');
}

function updateProgress(percentage) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    
    progressFill.style.width = percentage + '%';
    progressText.textContent = percentage + '%';
}

function updateStatus(message) {
    const statusMessage = document.getElementById('status-message');
    statusMessage.textContent = message;
}

// Méthode de sélection de fichier (utilise Tkinter directement)
async function selectFileMethod() {
    // Utiliser directement Tkinter pour avoir le chemin complet
    await selectFile();
}

// Gestion de la sélection de fichier HTML
function handleFileSelect(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const filePath = file.path || file.webkitRelativePath || file.name;

        // Vérifier l'extension
        if (filePath.endsWith('.xlsx') || filePath.endsWith('.xls')) {
            document.getElementById('source-file').value = filePath;
            showNotification('Fichier sélectionné avec succès', 'success');
            updateStatus('Fichier Excel sélectionné');

            // Réinitialiser le bouton d'export (désactiver)
            resetExportButton();

            // Envoyer le chemin complet au backend
            setInputFile(filePath);
        } else {
            showNotification('Veuillez sélectionner un fichier Excel (.xlsx ou .xls)', 'error');
        }
    }
}

// Sélection de fichier avec Tkinter (fallback)
async function selectFile() {
    try {
        updateStatus('Sélection du fichier...');
        const result = await eel.select_file()();

        if (result.success) {
            document.getElementById('source-file').value = result.message.replace('Fichier sélectionné: ', '');
            showNotification('Fichier sélectionné avec succès', 'success');
            updateStatus('Fichier Excel sélectionné');

            // Réinitialiser le bouton d'export (désactiver)
            resetExportButton();
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de la sélection du fichier');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la sélection du fichier', 'error');
        updateStatus('Erreur');
    }
}

// Envoyer le fichier au backend
async function setInputFile(filePath) {
    try {
        const result = await eel.set_input_file(filePath)();
        if (!result.success) {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
    }
}

// Réinitialiser les boutons d'export et Renault (les désactiver)
function resetExportButton() {
    const printBtn = document.getElementById('btn-print');
    if (printBtn) {
        printBtn.disabled = true;
        printBtn.innerHTML = '<span>🖨️</span> Options d\'Impression';
    }

    // Désactiver aussi le bouton Renault car aucun fichier avec codes n'est généré
    const btnRenault = document.querySelector('.btn-renault');
    if (btnRenault) {
        btnRenault.disabled = true;
        btnRenault.innerHTML = '<span>🏷️</span> Étiquettes Renault';
    }
}

// Variable globale pour suivre l'état des opérations
let isOperationRunning = false;
let currentOperation = null;

// Afficher/masquer le bouton d'annulation
function showCancelButton() {
    const cancelBtn = document.getElementById('btn-cancel');
    if (cancelBtn) {
        cancelBtn.style.display = 'block';
        cancelBtn.disabled = false;
        isOperationRunning = true;
    }
}

function hideCancelButton() {
    const cancelBtn = document.getElementById('btn-cancel');
    if (cancelBtn) {
        cancelBtn.style.display = 'none';
        cancelBtn.disabled = true;
        isOperationRunning = false;
        currentOperation = null;
    }
}

// Fonction d'annulation
async function cancelOperation() {
    try {
        showNotification('Annulation en cours...', 'info');

        // Appeler la fonction d'annulation côté backend
        const result = await eel.cancel_current_operation()();

        if (result.success) {
            showNotification('Opération annulée avec succès', 'success');
            updateStatus('Opération annulée');
        } else {
            showNotification('Erreur lors de l\'annulation: ' + result.message, 'error');
        }

        // Réinitialiser l'interface
        resetInterface();

    } catch (error) {
        console.error('Erreur annulation:', error);
        showNotification('Erreur lors de l\'annulation', 'error');
        resetInterface();
    }
}

// Réinitialiser l'interface après annulation
function resetInterface() {
    hideCancelButton();

    // Réactiver les boutons principaux
    const generateBtn = document.querySelector('.btn-generate');
    const printBtn = document.getElementById('btn-print');

    if (generateBtn) {
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<span>🚀</span> Générer les Codes';
    }

    if (printBtn) {
        printBtn.disabled = true;
        printBtn.innerHTML = '<span>🖨️</span> Options d\'Impression';
    }

    // Désactiver aussi le bouton Renault
    const btnRenault = document.querySelector('.btn-renault');
    if (btnRenault) {
        btnRenault.disabled = true;
        btnRenault.innerHTML = '<span>🏷️</span> Étiquettes Renault';
    }

    // Réinitialiser la barre de progression
    updateProgress(0);
}

// Sélection de dossier
async function selectFolder() {
    try {
        updateStatus('Sélection du dossier...');
        const result = await eel.select_folder()();
        
        if (result.success) {
            document.getElementById('dest-folder').value = result.message.replace('Dossier sélectionné: ', '');
            showNotification('Dossier sélectionné avec succès', 'success');
            updateStatus('Dossier de destination sélectionné');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de la sélection du dossier');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la sélection du dossier', 'error');
        updateStatus('Erreur');
    }
}

// Démarrer la génération
async function startGeneration() {
    if (isGenerating) {
        showNotification('Génération déjà en cours...', 'warning');
        return;
    }

    // Debug info
    try {
        const debugInfo = await eel.debug_info()();
        console.log('Debug info:', debugInfo);

        // Afficher les informations de debug à l'utilisateur
        if (!debugInfo.input_exists) {
            showNotification(`Fichier non trouvé: ${debugInfo.input_file}`, 'error');
            return;
        }
        if (!debugInfo.output_exists) {
            showNotification(`Dossier non trouvé: ${debugInfo.output_folder}`, 'error');
            return;
        }
    } catch (e) {
        console.log('Erreur debug:', e);
    }

    // Vérifier les champs
    const sourceFile = document.getElementById('source-file').value;
    const destFolder = document.getElementById('dest-folder').value;

    if (!sourceFile || !destFolder) {
        showNotification('Veuillez sélectionner un fichier et un dossier de destination', 'error');
        return;
    }
    
    // Récupérer les options
    const generateBarcodes = document.getElementById('generate-barcodes').checked;
    const generateQR = document.getElementById('generate-qr').checked;
    
    if (!generateBarcodes && !generateQR) {
        showNotification('Veuillez sélectionner au moins une option de génération', 'error');
        return;
    }
    
    try {
        isGenerating = true;
        currentOperation = 'generation';
        updateStatus('Génération en cours... 0%');
        updateProgress(0);

        // Désactiver le bouton et afficher le bouton d'annulation
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span>⏳</span> Génération en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        showNotification('Démarrage de la génération...', 'info');
        
        // Démarrer la génération
        const result = await eel.start_generation(generateBarcodes, generateQR)();
        
        if (!result.success) {
            // Seulement en cas d'erreur de démarrage
            isGenerating = false;
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span>🚀</span> Générer les Codes';
            showNotification(result.message, 'error');
            updateStatus('Erreur lors du démarrage');
        }
        // Sinon, la génération continue en arrière-plan avec mises à jour temps réel

    } catch (error) {
        console.error('Erreur:', error);
        isGenerating = false;
        updateStatus('Erreur lors de la génération');
        showNotification('Erreur lors de la génération', 'error');

        // Réactiver le bouton en cas d'erreur
        const generateBtn = document.querySelector('.btn-generate');
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<span>🚀</span> Générer les Codes';
    }
}

// Fonction appelée depuis Python pour mettre à jour le progrès EN TEMPS RÉEL
function update_progress(percentage) {
    updateProgress(percentage);
    // Afficher le titre avec le pourcentage
    updateStatus(`Génération en cours... ${percentage}%`);
}

// Exposer la fonction globalement pour pywebview
window.update_progress = update_progress;

// Fonction appelée depuis Python quand la génération est terminée
function generation_complete(result) {
    console.log('generation_complete appelée avec:', result);
    isGenerating = false;

    // Masquer le bouton d'annulation
    hideCancelButton();

    // Réactiver le bouton de génération
    const generateBtn = document.querySelector('.btn-generate');
    generateBtn.disabled = false;
    generateBtn.innerHTML = '<span>🚀</span> Générer les Codes';

    // Activer le bouton d'export si la génération a réussi
    const printBtn = document.getElementById('btn-print');

    console.log('Bouton trouvé:', {
        printBtn: printBtn ? 'trouvé' : 'non trouvé'
    });

    if (result.success) {
        console.log('Génération réussie, activation des boutons');
        printBtn.disabled = false;

        // Activer aussi le bouton Renault car un fichier avec codes a été généré
        const btnRenault = document.querySelector('.btn-renault');
        if (btnRenault) {
            btnRenault.disabled = false;
            console.log('Bouton Renault activé - fichier avec codes disponible');
        }

        showNotification(result.message, 'success');
        updateStatus('Génération terminée avec succès !');
        updateProgress(100);
    } else {
        console.log('Génération échouée, désactivation des boutons');
        printBtn.disabled = true;

        // Désactiver aussi le bouton Renault
        const btnRenault = document.querySelector('.btn-renault');
        if (btnRenault) {
            btnRenault.disabled = true;
        }

        showNotification(result.message, 'error');
        updateStatus('Erreur lors de la génération');
        updateProgress(0);
    }
}

// Exposer la fonction globalement pour pywebview
window.generation_complete = generation_complete;

// Fonction pour mettre à jour le statut depuis Python
function updateStatusFromPython(message) {
    updateStatus(message);
}

// Exposer la fonction globalement pour pywebview
window.updateStatus = updateStatusFromPython;

// Fonction pour afficher les options d'impression
function showPrintOptions() {
    const modal = document.getElementById('print-options-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

// Fonction pour fermer la modal des options d'impression
function closePrintOptions() {
    const modal = document.getElementById('print-options-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Fonction pour gérer le choix d'impression
function handlePrintChoice(choice) {
    closePrintOptions();

    switch(choice) {
        case 'export':
            // Ouvrir la modal d'export avec les options
            showExportOptionsModal();
            break;
        case 'print':
            // Ouvrir la modal d'impression directe
            showDirectPrintModal();
            break;
        case 'cancel':
            // Juste fermer la modal (déjà fait)
            break;
    }
}

// Fonctions pour la modal d'export
function showExportOptionsModal() {
    const modal = document.getElementById('export-options-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeExportOptions() {
    const modal = document.getElementById('export-options-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Fonctions pour la modal d'impression directe
function showDirectPrintModal() {
    const modal = document.getElementById('direct-print-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeDirectPrint() {
    const modal = document.getElementById('direct-print-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Fonction pour impression directe
async function printDirectly() {
    // Fermer la modal d'abord
    closeDirectPrint();

    if (isOperationRunning) {
        showNotification('Une opération est déjà en cours', 'warning');
        return;
    }

    try {
        isOperationRunning = true;
        currentOperation = 'print';

        // Vérifier quels types de codes sont sélectionnés
        const printBarcodes = document.getElementById('print-barcodes').checked;
        const printQR = document.getElementById('print-qr').checked;

        // Vérifier qu'au moins un type est sélectionné
        if (!printBarcodes && !printQR) {
            showNotification('Veuillez sélectionner au moins un type de code à imprimer', 'error');
            isOperationRunning = false;
            return;
        }

        updateStatus('Impression en cours...');
        showNotification('Démarrage de l\'impression...', 'info');

        // Désactiver le bouton pendant l'impression
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = true;
        printBtn.innerHTML = '<span>⏳</span> Impression en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        // Appeler la fonction d'impression directe
        const result = await eel.print_directly(printBarcodes, printQR)();

        if (result.success) {
            showNotification(result.message, 'success');
            updateStatus('Impression terminée !');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de l\'impression');
        }

    } catch (error) {
        console.error('Erreur impression:', error);
        showNotification('Erreur lors de l\'impression', 'error');
        updateStatus('Erreur lors de l\'impression');
    } finally {
        // Réactiver le bouton et masquer le bouton d'annulation
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = false;
        printBtn.innerHTML = '<span>🖨️</span> Options d\'Impression';

        // Masquer le bouton d'annulation
        hideCancelButton();
        isOperationRunning = false;
    }
}

// Fonction pour exporter les images haute résolution
async function exportImages() {
    // Fermer les modals d'abord
    closePrintOptions();
    closeExportOptions();

    try {
        // Vérifier quels types de codes sont sélectionnés
        const generateBarcodes = document.getElementById('generate-barcodes').checked;
        const generateQR = document.getElementById('generate-qr').checked;

        // Vérifier qu'au moins un type est sélectionné
        if (!generateBarcodes && !generateQR) {
            showNotification('Veuillez sélectionner au moins un type de code à exporter', 'error');
            return;
        }

        currentOperation = 'export';
        updateStatus('Export des images en cours...');
        showNotification('Démarrage de l\'export des images...', 'info');

        // Désactiver le bouton pendant l'export et afficher le bouton d'annulation
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = true;
        printBtn.innerHTML = '<span>⏳</span> Export en cours...';

        // Afficher le bouton d'annulation
        showCancelButton();

        // Passer les options au backend
        const result = await eel.export_images_for_print(generateBarcodes, generateQR)();

        if (result.success) {
            showNotification(result.message, 'success');
            updateStatus('Export des images terminé !');
        } else {
            showNotification(result.message, 'error');
            updateStatus('Erreur lors de l\'export');
        }

    } catch (error) {
        console.error('Erreur export:', error);
        showNotification('Erreur lors de l\'export des images', 'error');
        updateStatus('Erreur lors de l\'export');
    } finally {
        // Réactiver le bouton et masquer le bouton d'annulation
        const printBtn = document.getElementById('btn-print');
        printBtn.disabled = false;
        printBtn.innerHTML = '<span>🖨️</span> Options d\'Impression';

        // Masquer le bouton d'annulation
        hideCancelButton();
    }
}

// Fonction exposée pour afficher le dialogue de fichier existant
eel.expose(file_exists_dialog);
function file_exists_dialog(filename) {
    const modal = document.getElementById('file-exists-modal');
    const filenameElement = document.getElementById('existing-filename');

    filenameElement.textContent = filename;
    modal.style.display = 'flex';
}

// Gérer le choix de l'utilisateur pour le fichier existant
async function handleFileChoice(choice) {
    const modal = document.getElementById('file-exists-modal');
    modal.style.display = 'none';

    try {
        await eel.user_file_choice(choice)();

        if (choice === 'overwrite') {
            showNotification('Le fichier sera écrasé', 'warning');
        } else if (choice === 'rename') {
            showNotification('Un nouveau nom sera généré automatiquement', 'info');
        } else if (choice === 'cancel') {
            showNotification('Génération annulée', 'info');
            updateStatus('Génération annulée par l\'utilisateur');

            // Réactiver les boutons
            const generateBtn = document.querySelector('.btn-generate');
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span>🚀</span> Générer les Codes';
            isGenerating = false;
        }
    } catch (error) {
        console.error('Erreur choix fichier:', error);
        showNotification('Erreur lors du traitement du choix', 'error');
    }
}

// Gestion des raccourcis clavier
document.addEventListener('keydown', function(event) {
    // Ctrl+O pour ouvrir un fichier
    if (event.ctrlKey && event.key === 'o') {
        event.preventDefault();
        selectFile();
    }
    
    // Ctrl+D pour sélectionner un dossier
    if (event.ctrlKey && event.key === 'd') {
        event.preventDefault();
        selectFolder();
    }
    
    // Ctrl+G pour générer
    if (event.ctrlKey && event.key === 'g') {
        event.preventDefault();
        startGeneration();
    }
    
    // Échap pour fermer les notifications
    if (event.key === 'Escape') {
        closeNotification();
    }
});

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    console.log('SOMACA Générateur de Codes-Barres - Interface Web chargée');
    updateStatus('Prêt à générer les codes');
    
    // Ajouter des tooltips aux boutons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Gestion du drag & drop pour les fichiers
document.addEventListener('DOMContentLoaded', function() {
    const sourceInput = document.getElementById('source-file');
    const fileContainer = sourceInput.parentElement;
    
    // Prévenir le comportement par défaut
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        fileContainer.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // Highlight lors du drag
    ['dragenter', 'dragover'].forEach(eventName => {
        fileContainer.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        fileContainer.addEventListener(eventName, unhighlight, false);
    });
    
    // Gestion du drop
    fileContainer.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        fileContainer.classList.add('drag-over');
    }
    
    function unhighlight(e) {
        fileContainer.classList.remove('drag-over');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            const file = files[0];
            if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                sourceInput.value = file.name;
                showNotification('Fichier déposé avec succès', 'success');
            } else {
                showNotification('Veuillez déposer un fichier Excel (.xlsx ou .xls)', 'error');
            }
        }
    }
});



// Styles pour le drag & drop
const style = document.createElement('style');
style.textContent = `
    .drag-over {
        border-color: var(--primary-yellow) !important;
        background-color: rgba(255, 211, 32, 0.1) !important;
    }
`;
document.head.appendChild(style);

// Fonction pour générer les étiquettes Renault
async function generateRenaultEtiquettes() {
    try {
        // Désactiver le bouton pendant la vérification
        const btnRenault = document.querySelector('.btn-renault');
        btnRenault.disabled = true;
        btnRenault.innerHTML = '<span>⏳</span> Vérification...';

        updateStatus('Vérification des codes existants dans le fichier Excel généré...');

        // Appeler la fonction Python pour vérifier
        let result;
        if (typeof pywebview !== 'undefined' && pywebview.api) {
            // Application native
            result = await pywebview.api.generate_renault_etiquettes();
        } else if (typeof eel !== 'undefined') {
            // Application web
            result = await eel.generate_renault_etiquettes()();
        } else {
            throw new Error('API non disponible');
        }

        if (result.success && result.action === 'show_choice_modal') {
            // Afficher la modal de choix
            showRenaultChoiceModal();
        } else if (result.success) {
            updateProgress(100);
            updateStatus('Étiquettes Renault générées avec succès !');
            showNotification(result.message, 'success');

            // Proposer d'ouvrir le dossier
            if (confirm('Voulez-vous ouvrir le dossier contenant les étiquettes ?')) {
                if (typeof pywebview !== 'undefined' && pywebview.api) {
                    try {
                        await pywebview.api.open_folder_explorer(result.folder);
                    } catch (e) {
                        console.log('Ouverture dossier non supportée');
                    }
                } else if (typeof eel !== 'undefined') {
                    await eel.open_folder(result.folder)();
                }
            }
        } else {
            updateStatus('Erreur lors de la génération des étiquettes Renault');
            showNotification(result.message, 'error');
        }

    } catch (error) {
        console.error('Erreur:', error);
        updateStatus('Erreur lors de la génération des étiquettes Renault');
        showNotification('Erreur lors de la génération des étiquettes Renault', 'error');
    } finally {
        // Réactiver le bouton
        const btnRenault = document.querySelector('.btn-renault');
        btnRenault.disabled = false;
        btnRenault.innerHTML = '<span>🏷️</span> Étiquettes Renault';
    }
}

// Fonction pour afficher la modal de choix des types d'étiquettes
function showRenaultChoiceModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <h3><i class="fas fa-tags"></i> Étiquettes Renault</h3>
            <p>Choisissez les types d'étiquettes à générer :</p>

            <div class="choice-options">
                <label class="choice-option">
                    <input type="checkbox" id="include-barcodes" checked>
                    <i class="fas fa-barcode"></i> Codes-barres
                </label>

                <label class="choice-option">
                    <input type="checkbox" id="include-qr" checked>
                    <i class="fas fa-qrcode"></i> QR Codes
                </label>
            </div>

            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeRenaultChoiceModal()">
                    <i class="fas fa-times"></i> Annuler
                </button>
                <button class="btn btn-success" onclick="generateRenaultWithChoice()">
                    <i class="fas fa-check"></i> Générer
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Ajouter les styles CSS pour la modal
    if (!document.getElementById('renault-choice-styles')) {
        const style = document.createElement('style');
        style.id = 'renault-choice-styles';
        style.textContent = `
            .choice-options {
                margin: 20px 0;
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .choice-option {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .choice-option:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }

            .choice-option input[type="checkbox"] {
                width: 20px;
                height: 20px;
            }

            .choice-option i {
                font-size: 24px;
                color: #007bff;
            }
        `;
        document.head.appendChild(style);
    }
}

// Fonction pour fermer la modal de choix
function closeRenaultChoiceModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Fonction pour générer avec le choix utilisateur
async function generateRenaultWithChoice() {
    try {
        const includeBarcodes = document.getElementById('include-barcodes').checked;
        const includeQr = document.getElementById('include-qr').checked;

        if (!includeBarcodes && !includeQr) {
            showNotification('Veuillez sélectionner au moins un type de code', 'error');
            return;
        }

        // Fermer la modal
        closeRenaultChoiceModal();

        // Désactiver le bouton
        const btnRenault = document.querySelector('.btn-renault');
        btnRenault.disabled = true;
        btnRenault.innerHTML = '<span>⏳</span> Génération...';

        updateStatus('Génération des étiquettes Renault en cours... 0%');
        updateProgress(0);

        // Appeler la fonction avec les choix
        let result;
        if (typeof pywebview !== 'undefined' && pywebview.api) {
            result = await pywebview.api.generate_renault_etiquettes_with_choice(includeBarcodes, includeQr);
        } else if (typeof eel !== 'undefined') {
            result = await eel.generate_renault_etiquettes_with_choice(includeBarcodes, includeQr)();
        } else {
            throw new Error('API non disponible');
        }

        if (result.success) {
            updateProgress(100);
            updateStatus('Étiquettes Renault générées avec succès !');
            showNotification(result.message, 'success');

            // Proposer d'ouvrir le dossier
            if (confirm('Voulez-vous ouvrir le dossier contenant les étiquettes ?')) {
                if (typeof pywebview !== 'undefined' && pywebview.api) {
                    try {
                        await pywebview.api.open_folder_explorer(result.folder);
                    } catch (e) {
                        console.log('Ouverture dossier non supportée');
                    }
                } else if (typeof eel !== 'undefined') {
                    await eel.open_folder(result.folder)();
                }
            }
        } else {
            updateStatus('Erreur lors de la génération des étiquettes Renault');
            showNotification(result.message, 'error');
        }

    } catch (error) {
        console.error('Erreur:', error);
        updateStatus('Erreur lors de la génération des étiquettes Renault');
        showNotification('Erreur lors de la génération des étiquettes Renault', 'error');
    } finally {
        // Réactiver le bouton
        const btnRenault = document.querySelector('.btn-renault');
        btnRenault.disabled = false;
        btnRenault.innerHTML = '<span>🏷️</span> Étiquettes Renault';
    }
}
